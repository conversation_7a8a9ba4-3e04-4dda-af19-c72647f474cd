# Guida al Deploy su Render.com

Questa guida ti aiuterà a deployare FitForMe su Render.com.

## 📋 Prerequisiti

1. Account GitHub con il repository del progetto
2. Account Render.com (gratuito)
3. Chiave API di Google Gemini

## 🚀 Passi per il Deploy

### 1. Preparazione del Repository

Assicurati che il tuo repository GitHub contenga:
- Tutti i file del progetto
- Il file `.env` NON deve essere committato (è già in .gitignore)
- Il file `package.json` con gli script corretti

### 2. Configurazione su Render

1. **Accedi a Render.com**
   - Vai su [render.com](https://render.com)
   - Accedi con il tuo account GitHub

2. **Crea un nuovo Web Service**
   - Clicca "New +" → "Web Service"
   - Connetti il tuo repository GitHub
   - Seleziona il repository `fitforme-app`

3. **Configurazioni del Servizio**
   ```
   Name: fitforme-app (o il nome che preferisci)
   Environment: Node
   Region: Frankfurt (EU Central) o la più vicina a te
   Branch: main (o il tuo branch principale)
   Root Directory: (lascia vuoto)
   ```

4. **Build & Deploy Settings**
   ```
   Build Command: npm install && npm run build
   Start Command: npm start
   ```

5. **Variabili d'Ambiente**
   
   Nella sezione "Environment Variables", aggiungi:
   ```
   GEMINI_API_KEY = your_actual_gemini_api_key_here
   NODE_ENV = production
   ```

6. **Piano di Pricing**
   - Seleziona "Free" per iniziare
   - Puoi sempre fare upgrade in seguito

### 3. Deploy

1. Clicca "Create Web Service"
2. Render inizierà automaticamente il build
3. Il processo richiederà alcuni minuti
4. Una volta completato, riceverai un URL pubblico

## 🔧 Configurazioni Avanzate

### Auto-Deploy

Render può fare auto-deploy quando fai push su GitHub:
- Vai nelle impostazioni del servizio
- Abilita "Auto-Deploy"
- Ogni push sul branch principale triggerera un nuovo deploy

### Custom Domain

Per usare un dominio personalizzato:
1. Vai in "Settings" → "Custom Domains"
2. Aggiungi il tuo dominio
3. Configura i DNS records come indicato

### Monitoring

Render fornisce:
- Logs in tempo reale
- Metriche di performance
- Alerting automatico

## 🐛 Troubleshooting

### Build Fails

Se il build fallisce:
1. Controlla i logs nella dashboard Render
2. Verifica che tutte le dipendenze siano in `package.json`
3. Assicurati che gli script `build` e `start` siano corretti

### App non si avvia

Se l'app non si avvia:
1. Controlla che `GEMINI_API_KEY` sia impostata
2. Verifica i logs per errori specifici
3. Assicurati che la porta sia configurata correttamente

### Errori API Gemini

Se le chiamate a Gemini falliscono:
1. Verifica che la chiave API sia valida
2. Controlla i limiti di quota dell'API
3. Verifica che l'API sia abilitata nel tuo progetto Google

## 📊 Monitoraggio

### Logs

Per vedere i logs:
1. Vai nella dashboard del servizio
2. Clicca su "Logs"
3. Puoi filtrare per tipo di log

### Metriche

Render fornisce metriche su:
- CPU usage
- Memory usage
- Response times
- Error rates

## 💰 Costi

### Piano Free

Il piano gratuito include:
- 750 ore/mese di runtime
- 512MB RAM
- Condivisione CPU
- Sleep dopo 15 minuti di inattività

### Upgrade

Per traffico più alto, considera:
- Piano Starter ($7/mese)
- Piano Standard ($25/mese)
- Più RAM e CPU dedicata

## 🔒 Sicurezza

### Variabili d'Ambiente

- Mai committare chiavi API nel codice
- Usa sempre le environment variables di Render
- Rigenera le chiavi se compromesse

### HTTPS

- Render fornisce HTTPS automaticamente
- Certificati SSL gestiti automaticamente
- Redirect HTTP → HTTPS abilitato di default

## 📞 Supporto

Se hai problemi:
1. Controlla la [documentazione Render](https://render.com/docs)
2. Usa il supporto chat di Render
3. Controlla i logs per errori specifici

---

**Tip**: Testa sempre l'app localmente prima del deploy per evitare problemi in produzione!
