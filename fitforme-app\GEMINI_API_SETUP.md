# Come Ottenere la Chiave API di Google Gemini

Questa guida ti aiuterà a ottenere la chiave API necessaria per far funzionare FitForMe.

## 🔑 Passi per Ottenere la Chiave API

### 1. Accedi a Google AI Studio

1. Vai su [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Accedi con il tuo account Google
3. Se non hai un account Google, creane uno gratuitamente

### 2. Crea una Nuova API Key

1. Una volta loggato, vedrai la pagina "API Keys"
2. Clicca sul pulsante **"Create API Key"**
3. Seleziona un progetto Google Cloud esistente o creane uno nuovo:
   - Se non hai progetti, clicca "Create API key in new project"
   - Se hai già progetti, seleziona quello che preferisci

### 3. Copia la Chiave API

1. Una volta creata, la chiave API apparirà nella lista
2. Clicca sull'icona "Copy" accanto alla chiave
3. **IMPORTANTE**: Salva questa chiave in un posto sicuro!

### 4. Configura l'Applicazione

1. Apri il file `.env` nella root del progetto FitForMe
2. Sostituisci `your_gemini_api_key_here` con la tua chiave reale:
   ```env
   GEMINI_API_KEY=AIzaSyC-tu_chiave_api_qui
   ```
3. Salva il file

## 🔒 Sicurezza della Chiave API

### ⚠️ IMPORTANTE - Non Condividere Mai la Tua Chiave

- **NON** committare mai la chiave API su GitHub
- **NON** condividerla in chat, email o forum
- **NON** includerla in screenshot o video

### ✅ Buone Pratiche

- Usa sempre file `.env` per le chiavi API
- Aggiungi `.env` al tuo `.gitignore`
- Rigenera la chiave se pensi sia stata compromessa
- Usa chiavi diverse per sviluppo e produzione

## 💰 Costi e Limiti

### Piano Gratuito

Google Gemini offre un piano gratuito che include:
- 15 richieste al minuto
- 1 milione di token al giorno
- 1500 richieste al giorno

### Monitoraggio dell'Uso

1. Vai su [Google AI Studio](https://makersuite.google.com)
2. Clicca su "Usage" per vedere il tuo consumo
3. Monitora regolarmente per evitare di superare i limiti

### Upgrade a Piano Pagamento

Se hai bisogno di più richieste:
1. Vai su [Google Cloud Console](https://console.cloud.google.com)
2. Abilita la fatturazione per il tuo progetto
3. I prezzi sono molto competitivi (circa $0.001 per 1000 token)

## 🛠️ Risoluzione Problemi

### Errore "API Key Invalid"

Se ricevi questo errore:
1. Verifica che la chiave sia copiata correttamente
2. Assicurati che non ci siano spazi extra
3. Controlla che l'API Gemini sia abilitata nel tuo progetto

### Errore "Quota Exceeded"

Se superi i limiti:
1. Aspetta che si resetti il limite (di solito ogni 24 ore)
2. Considera l'upgrade a un piano pagamento
3. Ottimizza l'app per fare meno richieste

### Errore "Permission Denied"

Se hai problemi di permessi:
1. Verifica che l'API Gemini sia abilitata
2. Controlla che il progetto Google Cloud sia attivo
3. Assicurati di avere i permessi necessari sul progetto

## 🔄 Gestione delle Chiavi API

### Rotazione delle Chiavi

Per sicurezza, cambia periodicamente le chiavi:
1. Crea una nuova chiave API
2. Aggiorna il file `.env` con la nuova chiave
3. Testa che tutto funzioni
4. Elimina la vecchia chiave

### Chiavi Multiple

Puoi creare chiavi diverse per:
- Sviluppo locale
- Staging/Test
- Produzione

### Restrizioni API

Per maggiore sicurezza, puoi restringere le chiavi:
1. Vai su Google Cloud Console
2. Seleziona "Credentials"
3. Modifica la chiave API
4. Aggiungi restrizioni per IP o referrer

## 📞 Supporto

Se hai problemi:
1. Controlla la [documentazione ufficiale](https://ai.google.dev/docs)
2. Visita il [forum della community](https://discuss.ai.google.dev)
3. Contatta il supporto Google Cloud se hai un piano pagamento

---

**Ricorda**: La chiave API è come una password - tienila sempre segreta e sicura!
