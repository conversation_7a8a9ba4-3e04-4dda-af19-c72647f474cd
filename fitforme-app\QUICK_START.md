# 🚀 Quick Start - FitForMe

Guida rapida per iniziare subito con FitForMe!

## ⚡ Setup in 5 Minuti

### 1. Configura la Chiave API
```bash
# Copia il file di esempio
cp .env.example .env

# Modifica .env e inserisci la tua chiave API di Gemini
# GEMINI_API_KEY=your_actual_api_key_here
```

### 2. Avvia l'App
```bash
# Opzione A: Script automatico (Windows)
start-dev.bat

# Opzione B: Script automatico (Mac/Linux)
./start-dev.sh

# Opzione C: Manuale
# Terminal 1
npm run server

# Terminal 2
npm run dev
```

### 3. Apri il Browser
Vai su: http://localhost:5173

## 🎯 Come Usare

1. **Carica la tua foto** (obbligatoria)
2. **Aggiungi vestiti** (opzionali):
   - 🎩 Cappello
   - 👕 Maglietta/Giacca  
   - 👖 Pantaloni/Gonna
   - 👟 Scarpe
3. **<PERSON><PERSON><PERSON> "Prova Virtualmente!"**
4. **Aspetta il risultato** dall'AI

## 📁 File Importanti

- `README.md` - Documentazione completa
- `DEPLOY.md` - Guida per il deploy su Render
- `GEMINI_API_SETUP.md` - Come ottenere la chiave API
- `.env.example` - Template per le variabili d'ambiente

## 🆘 Problemi Comuni

### Server non si avvia
```bash
# Controlla che la porta 3001 sia libera
netstat -an | findstr :3001

# Installa le dipendenze se mancanti
npm install
```

### Errore API Key
- Verifica che `.env` contenga la chiave corretta
- Controlla che non ci siano spazi extra
- Leggi `GEMINI_API_SETUP.md` per dettagli

### Upload non funziona
- Usa solo immagini (JPG, PNG, GIF)
- Massimo 10MB per file
- Almeno la foto personale è obbligatoria

## 🌐 Deploy su Render

1. Push su GitHub
2. Connetti repository su Render.com
3. Imposta variabili d'ambiente
4. Deploy automatico!

Leggi `DEPLOY.md` per dettagli completi.

## 📞 Supporto

- 📖 Documentazione: `README.md`
- 🚀 Deploy: `DEPLOY.md`  
- 🔑 API Setup: `GEMINI_API_SETUP.md`
- 🐛 Issues: GitHub Issues

---

**Buon divertimento con FitForMe! 🎉**
