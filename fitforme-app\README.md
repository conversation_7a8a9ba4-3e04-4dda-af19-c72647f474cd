# FitForMe - Virtual Try-On App

Una webapp innovativa che utilizza l'intelligenza artificiale di Google Gemini per creare esperienze di prova virtuale di abbigliamento.

## 🌟 Caratteristiche

- **Upload Multiplo**: Carica la tua foto e fino a 4 capi di abbigliamento
- **AI-Powered**: Utilizza Google Gemini per generare risultati realistici
- **Interface Intuitiva**: Design moderno e user-friendly con drag & drop
- **Responsive**: Funziona perfettamente su desktop e mobile
- **Deploy Ready**: Configurato per il deployment su Render.com

## 📋 Tipi di Immagini Supportate

1. **👤 La tua foto** (obbligatoria) - Una foto di te stesso
2. **🎩 Cappello** (opzionale) - Cappelli, berretti, etc.
3. **👕 Maglietta/Giacca** (opzionale) - Top, camicie, giacche
4. **👖 Pantaloni/Gonna** (opzionale) - Bottom, gonne, shorts
5. **👟 Scarpe** (opzionale) - Calzature di ogni tipo

## 🚀 Setup Locale

### Prerequisiti

- Node.js (versione 18 o superiore)
- npm o yarn
- Chiave API di Google Gemini

### Installazione

1. **Clona il repository**
   ```bash
   git clone <repository-url>
   cd fitforme-app
   ```

2. **Installa le dipendenze**
   ```bash
   npm install
   ```

3. **Configura le variabili d'ambiente**
   
   Modifica il file `.env` nella root del progetto:
   ```env
   GEMINI_API_KEY=your_actual_gemini_api_key_here
   PORT=3001
   NODE_ENV=development
   ```

4. **Avvia l'applicazione**
   
   Per sviluppo (frontend + backend):
   ```bash
   # Terminal 1 - Backend
   npm run server
   
   # Terminal 2 - Frontend
   npm run dev
   ```

5. **Accedi all'app**
   
   Apri il browser su `http://localhost:5173`

## 🌐 Deploy su Render.com

### Preparazione

1. **Crea un account su Render.com**

2. **Prepara il repository**
   - Assicurati che tutti i file siano committati
   - Il file `.env` non deve essere committato (è già in .gitignore)

### Configurazione su Render

1. **Crea un nuovo Web Service**
   - Connetti il tuo repository GitHub
   - Seleziona il branch principale

2. **Configurazioni Build**
   ```
   Build Command: npm install && npm run build
   Start Command: npm start
   ```

3. **Variabili d'Ambiente**
   
   Aggiungi queste variabili nell'interfaccia di Render:
   ```
   GEMINI_API_KEY=your_actual_gemini_api_key_here
   NODE_ENV=production
   ```

4. **Deploy**
   - Clicca "Create Web Service"
   - Render farà automaticamente il build e deploy

## 🔧 Struttura del Progetto

```
fitforme-app/
├── src/
│   ├── components/
│   │   ├── VirtualTryOn.jsx      # Componente principale
│   │   ├── ImageUploader.jsx     # Upload delle immagini
│   │   ├── ResultDisplay.jsx     # Visualizzazione risultati
│   │   └── *.css                 # Stili dei componenti
│   ├── App.jsx                   # App principale
│   └── main.jsx                  # Entry point
├── server/
│   └── index.js                  # Server Express
├── uploads/                      # Cartella per file caricati
├── .env                          # Variabili d'ambiente (locale)
└── package.json                  # Dipendenze e scripts
```

## 🔑 Ottenere la Chiave API di Gemini

1. Vai su [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Accedi con il tuo account Google
3. Clicca "Create API Key"
4. Copia la chiave e usala nel file `.env`

## 📱 Come Usare l'App

1. **Carica la tua foto** - Obbligatoria per il processo
2. **Aggiungi i vestiti** - Carica le immagini dei capi che vuoi provare
3. **Clicca "Prova Virtualmente!"** - L'AI elaborerà le immagini
4. **Visualizza il risultato** - Vedrai la descrizione del look generato

## 🛠️ Tecnologie Utilizzate

### Frontend
- **React 19** - Framework UI
- **Vite** - Build tool e dev server
- **CSS3** - Styling moderno con gradients e animazioni
- **Axios** - HTTP client

### Backend
- **Node.js** - Runtime JavaScript
- **Express** - Web framework
- **Multer** - File upload middleware
- **Google Generative AI** - Integrazione Gemini

## 🔒 Sicurezza

- Le immagini sono salvate temporaneamente sul server
- Le chiavi API sono gestite tramite variabili d'ambiente
- Validazione dei tipi di file per sicurezza
- Limite di dimensione file (10MB)

## 🐛 Troubleshooting

### Errori Comuni

1. **"GEMINI_API_KEY not found"**
   - Verifica che la chiave API sia impostata correttamente nel file `.env`

2. **Errore di upload**
   - Controlla che il file sia un'immagine (JPG, PNG, GIF)
   - Verifica che la dimensione sia sotto i 10MB

3. **Server non si avvia**
   - Controlla che la porta 3001 sia libera
   - Verifica che tutte le dipendenze siano installate

### Log e Debug

Per vedere i log del server:
```bash
npm run server
```

I log includeranno informazioni su upload, errori API, e richieste.

## 📄 Licenza

Questo progetto è rilasciato sotto licenza MIT.

## 🤝 Contributi

I contributi sono benvenuti! Per favore:

1. Fai un fork del progetto
2. Crea un branch per la tua feature
3. Committa le modifiche
4. Fai un push del branch
5. Apri una Pull Request

## 📞 Supporto

Per problemi o domande, apri un issue nel repository GitHub.

---

**Nota**: Questa app utilizza l'AI di Google Gemini per generare descrizioni testuali del virtual try-on. Per risultati ottimali, usa immagini con buona illuminazione e alta qualità.
