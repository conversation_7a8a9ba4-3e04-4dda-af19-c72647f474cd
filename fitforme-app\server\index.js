import express from 'express';
import cors from 'cors';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../dist')));
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed!'));
    }
  }
});

// Helper function to convert file to base64
function fileToGenerativePart(path, mimeType) {
  return {
    inlineData: {
      data: Buffer.from(fs.readFileSync(path)).toString("base64"),
      mimeType
    },
  };
}

// Upload endpoint for multiple images
app.post('/api/upload', upload.fields([
  { name: 'person', maxCount: 1 },
  { name: 'hat', maxCount: 1 },
  { name: 'top', maxCount: 1 },
  { name: 'bottom', maxCount: 1 },
  { name: 'shoes', maxCount: 1 }
]), (req, res) => {
  try {
    const files = req.files;
    const uploadedFiles = {};
    
    Object.keys(files).forEach(key => {
      if (files[key] && files[key][0]) {
        uploadedFiles[key] = {
          filename: files[key][0].filename,
          path: files[key][0].path,
          originalname: files[key][0].originalname
        };
      }
    });
    
    res.json({
      success: true,
      files: uploadedFiles,
      message: 'Files uploaded successfully'
    });
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({
      success: false,
      message: 'Error uploading files',
      error: error.message
    });
  }
});

// Virtual try-on endpoint
app.post('/api/virtual-tryon', async (req, res) => {
  try {
    const { files } = req.body;
    
    if (!files || !files.person) {
      return res.status(400).json({
        success: false,
        message: 'Person image is required'
      });
    }

    // Prepare the prompt for Gemini
    let prompt = "Create a realistic virtual try-on image. Take the person in the first image and dress them with the clothing items provided. ";
    
    const imageParts = [];
    const uploadPath = path.join(__dirname, '../uploads');
    
    // Add person image
    const personPath = path.join(uploadPath, files.person.filename);
    imageParts.push(fileToGenerativePart(personPath, "image/jpeg"));
    
    // Add clothing items if provided
    const clothingItems = [];
    if (files.hat) {
      const hatPath = path.join(uploadPath, files.hat.filename);
      imageParts.push(fileToGenerativePart(hatPath, "image/jpeg"));
      clothingItems.push("hat/headwear");
    }
    if (files.top) {
      const topPath = path.join(uploadPath, files.top.filename);
      imageParts.push(fileToGenerativePart(topPath, "image/jpeg"));
      clothingItems.push("top/shirt/jacket");
    }
    if (files.bottom) {
      const bottomPath = path.join(uploadPath, files.bottom.filename);
      imageParts.push(fileToGenerativePart(bottomPath, "image/jpeg"));
      clothingItems.push("bottom/pants/skirt");
    }
    if (files.shoes) {
      const shoesPath = path.join(uploadPath, files.shoes.filename);
      imageParts.push(fileToGenerativePart(shoesPath, "image/jpeg"));
      clothingItems.push("shoes/footwear");
    }
    
    if (clothingItems.length > 0) {
      prompt += `The clothing items to try on are: ${clothingItems.join(', ')}. `;
    }
    
    prompt += "Make sure the clothing fits naturally on the person, maintaining realistic proportions, lighting, and shadows. The result should look like the person is actually wearing these clothes.";

    // Get the generative model
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    
    // Generate the virtual try-on
    const result = await model.generateContent([prompt, ...imageParts]);
    const response = await result.response;
    const text = response.text();
    
    res.json({
      success: true,
      result: text,
      message: 'Virtual try-on completed successfully'
    });
    
  } catch (error) {
    console.error('Virtual try-on error:', error);
    res.status(500).json({
      success: false,
      message: 'Error processing virtual try-on',
      error: error.message
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Server is running' });
});

// Serve React app for all other routes (only in production)
if (process.env.NODE_ENV === 'production') {
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../dist/index.html'));
  });
}

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV}`);
});
