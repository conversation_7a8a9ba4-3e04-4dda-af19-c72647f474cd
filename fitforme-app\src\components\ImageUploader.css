.image-uploader {
  width: 100%;
}

.upload-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.upload-item {
  position: relative;
}

.upload-item.required::before {
  content: '*';
  position: absolute;
  top: -10px;
  right: -10px;
  color: #dc3545;
  font-size: 1.5rem;
  font-weight: bold;
  z-index: 1;
}

.drop-zone {
  border: 3px dashed #dee2e6;
  border-radius: 15px;
  padding: 2rem 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9fa;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.drop-zone:hover {
  border-color: #667eea;
  background: #f0f2ff;
  transform: translateY(-2px);
}

.drop-zone.drag-active {
  border-color: #667eea;
  background: #e8ecff;
  transform: scale(1.02);
}

.drop-zone.has-file {
  border-color: #28a745;
  background: #f8fff9;
}

.drop-zone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.icon {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.label {
  font-weight: 600;
  color: #333;
  margin: 0;
  font-size: 1.1rem;
}

.instruction {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
  font-weight: 500;
}

.hint {
  color: #999;
  font-size: 0.8rem;
  margin: 0;
  font-style: italic;
}

.file-selected {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-img {
  max-width: 100%;
  max-height: 150px;
  border-radius: 10px;
  object-fit: cover;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.remove-file {
  position: absolute;
  top: -10px;
  right: -10px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  cursor: pointer;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(220, 53, 69, 0.3);
}

.remove-file:hover {
  background: #c82333;
  transform: scale(1.1);
}

.upload-actions {
  text-align: center;
  margin-top: 2rem;
}

.upload-button {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
}

.upload-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.6);
}

.upload-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

@media (max-width: 768px) {
  .upload-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .drop-zone {
    min-height: 150px;
    padding: 1.5rem 1rem;
  }
  
  .icon {
    font-size: 2rem;
  }
  
  .label {
    font-size: 1rem;
  }
  
  .preview-img {
    max-height: 120px;
  }
}

@media (max-width: 480px) {
  .drop-zone {
    min-height: 120px;
    padding: 1rem;
  }
  
  .icon {
    font-size: 1.5rem;
  }
  
  .label {
    font-size: 0.9rem;
  }
  
  .instruction,
  .hint {
    font-size: 0.8rem;
  }
}
