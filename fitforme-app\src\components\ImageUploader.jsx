import { useState, useRef } from 'react';
import axios from 'axios';
import './ImageUploader.css';

const ImageUploader = ({ onFilesUploaded }) => {
  const [dragActive, setDragActive] = useState({});
  const [uploading, setUploading] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState({});
  
  const fileInputRefs = {
    person: useRef(null),
    hat: useRef(null),
    top: useRef(null),
    bottom: useRef(null),
    shoes: useRef(null)
  };

  const uploadTypes = [
    { key: 'person', label: 'La tua foto', required: true, icon: '👤' },
    { key: 'hat', label: 'Cappello', required: false, icon: '🎩' },
    { key: 'top', label: 'Maglietta/Giacca', required: false, icon: '👕' },
    { key: 'bottom', label: 'Pantaloni/Gonna', required: false, icon: '👖' },
    { key: 'shoes', label: 'Scarpe', required: false, icon: '👟' }
  ];

  const handleDrag = (e, type) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(prev => ({ ...prev, [type]: true }));
    } else if (e.type === "dragleave") {
      setDragActive(prev => ({ ...prev, [type]: false }));
    }
  };

  const handleDrop = (e, type) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(prev => ({ ...prev, [type]: false }));
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0], type);
    }
  };

  const handleFileSelect = (file, type) => {
    if (!file.type.startsWith('image/')) {
      alert('Per favore seleziona solo file immagine!');
      return;
    }

    setSelectedFiles(prev => ({ ...prev, [type]: file }));
  };

  const handleInputChange = (e, type) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0], type);
    }
  };

  const uploadFiles = async () => {
    if (Object.keys(selectedFiles).length === 0) {
      alert('Seleziona almeno la tua foto!');
      return;
    }

    if (!selectedFiles.person) {
      alert('La tua foto è obbligatoria!');
      return;
    }

    setUploading(true);

    try {
      const formData = new FormData();
      Object.entries(selectedFiles).forEach(([type, file]) => {
        formData.append(type, file);
      });

      const response = await axios.post('/api/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.success) {
        onFilesUploaded(response.data.files);
        setSelectedFiles({});
        // Reset file inputs
        Object.values(fileInputRefs).forEach(ref => {
          if (ref.current) ref.current.value = '';
        });
      } else {
        alert('Errore durante l\'upload: ' + response.data.message);
      }
    } catch (error) {
      console.error('Upload error:', error);
      alert('Errore durante l\'upload dei file');
    } finally {
      setUploading(false);
    }
  };

  const removeFile = (type) => {
    setSelectedFiles(prev => {
      const newFiles = { ...prev };
      delete newFiles[type];
      return newFiles;
    });
    if (fileInputRefs[type].current) {
      fileInputRefs[type].current.value = '';
    }
  };

  return (
    <div className="image-uploader">
      <div className="upload-grid">
        {uploadTypes.map(({ key, label, required, icon }) => (
          <div key={key} className={`upload-item ${required ? 'required' : ''}`}>
            <div
              className={`drop-zone ${dragActive[key] ? 'drag-active' : ''} ${selectedFiles[key] ? 'has-file' : ''}`}
              onDragEnter={(e) => handleDrag(e, key)}
              onDragLeave={(e) => handleDrag(e, key)}
              onDragOver={(e) => handleDrag(e, key)}
              onDrop={(e) => handleDrop(e, key)}
              onClick={() => fileInputRefs[key].current?.click()}
            >
              <input
                ref={fileInputRefs[key]}
                type="file"
                accept="image/*"
                onChange={(e) => handleInputChange(e, key)}
                style={{ display: 'none' }}
              />
              
              {selectedFiles[key] ? (
                <div className="file-selected">
                  <img 
                    src={URL.createObjectURL(selectedFiles[key])} 
                    alt="Preview" 
                    className="preview-img"
                  />
                  <button 
                    className="remove-file"
                    onClick={(e) => {
                      e.stopPropagation();
                      removeFile(key);
                    }}
                  >
                    ✕
                  </button>
                </div>
              ) : (
                <div className="drop-zone-content">
                  <span className="icon">{icon}</span>
                  <p className="label">{label}</p>
                  <p className="instruction">
                    {required ? 'Obbligatorio' : 'Opzionale'}
                  </p>
                  <p className="hint">Clicca o trascina qui</p>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {Object.keys(selectedFiles).length > 0 && (
        <div className="upload-actions">
          <button 
            onClick={uploadFiles} 
            disabled={uploading}
            className="upload-button"
          >
            {uploading ? 'Caricamento...' : `Carica ${Object.keys(selectedFiles).length} file`}
          </button>
        </div>
      )}
    </div>
  );
};

export default ImageUploader;
