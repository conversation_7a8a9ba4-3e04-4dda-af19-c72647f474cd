.result-display {
  text-align: center;
  padding: 2rem;
}

.result-display h3 {
  color: #333;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  font-weight: 600;
}

.result-display.processing {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 20px;
  padding: 3rem 2rem;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container h3 {
  color: #495057;
  margin: 0;
  font-size: 1.5rem;
}

.loading-container p {
  color: #6c757d;
  margin: 0;
  font-size: 1rem;
}

.loading-steps {
  display: flex;
  gap: 2rem;
  margin-top: 1rem;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  opacity: 0.4;
  transition: opacity 0.3s ease;
}

.step.active {
  opacity: 1;
}

.step-icon {
  font-size: 2rem;
  background: white;
  padding: 1rem;
  border-radius: 50%;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.step span:last-child {
  font-size: 0.9rem;
  color: #495057;
  font-weight: 500;
}

.result-content {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 2rem;
  margin: 2rem 0;
  border: 1px solid #dee2e6;
}

.result-text {
  margin-bottom: 2rem;
}

.result-text p {
  color: #495057;
  line-height: 1.6;
  font-size: 1.1rem;
  margin: 0;
  white-space: pre-wrap;
}

.result-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.share-button,
.download-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.download-button {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.share-button:hover,
.download-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
}

.download-button:hover {
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.5);
}

.result-note {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 10px;
  padding: 1rem;
  margin-top: 2rem;
}

.result-note p {
  color: #856404;
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .loading-steps {
    flex-direction: column;
    gap: 1rem;
  }
  
  .step {
    flex-direction: row;
    gap: 1rem;
  }
  
  .step-icon {
    font-size: 1.5rem;
    padding: 0.8rem;
  }
  
  .result-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .share-button,
  .download-button {
    width: 100%;
    max-width: 250px;
  }
}

@media (max-width: 480px) {
  .result-display {
    padding: 1rem;
  }
  
  .result-display.processing {
    padding: 2rem 1rem;
  }
  
  .loading-container h3 {
    font-size: 1.3rem;
  }
  
  .result-content {
    padding: 1.5rem;
  }
  
  .result-text p {
    font-size: 1rem;
  }
}
