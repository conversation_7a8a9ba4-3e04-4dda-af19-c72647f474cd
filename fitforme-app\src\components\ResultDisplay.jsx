import './ResultDisplay.css';

const ResultDisplay = ({ result, isProcessing }) => {
  if (isProcessing) {
    return (
      <div className="result-display processing">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <h3>Creazione del tuo look virtuale...</h3>
          <p>Questo potrebbe richiedere alcuni secondi</p>
          <div className="loading-steps">
            <div className="step active">
              <span className="step-icon">📸</span>
              <span>Analisi delle immagini</span>
            </div>
            <div className="step active">
              <span className="step-icon">🤖</span>
              <span>Elaborazione AI</span>
            </div>
            <div className="step">
              <span className="step-icon">✨</span>
              <span>Generazione risultato</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!result) {
    return null;
  }

  return (
    <div className="result-display">
      <h3>Il tuo look virtuale è pronto!</h3>
      <div className="result-content">
        <div className="result-text">
          <p>{result}</p>
        </div>
        <div className="result-actions">
          <button 
            onClick={() => navigator.share && navigator.share({
              title: 'Il mio look virtuale - FitForMe',
              text: result
            })}
            className="share-button"
          >
            📤 Condividi
          </button>
          <button 
            onClick={() => {
              const element = document.createElement('a');
              const file = new Blob([result], { type: 'text/plain' });
              element.href = URL.createObjectURL(file);
              element.download = 'virtual-tryon-result.txt';
              document.body.appendChild(element);
              element.click();
              document.body.removeChild(element);
            }}
            className="download-button"
          >
            💾 Salva
          </button>
        </div>
      </div>
      
      <div className="result-note">
        <p>
          <strong>Nota:</strong> Questo è un risultato generato dall'AI. 
          Per risultati ottimali, usa immagini con buona illuminazione e alta qualità.
        </p>
      </div>
    </div>
  );
};

export default ResultDisplay;
