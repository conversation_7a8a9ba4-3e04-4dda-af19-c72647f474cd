import { useState } from 'react';
import axios from 'axios';
import ImageUploader from './ImageUploader';
import ResultDisplay from './ResultDisplay';
import './VirtualTryOn.css';

const VirtualTryOn = () => {
  const [uploadedFiles, setUploadedFiles] = useState({});
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);

  const handleFilesUploaded = (files) => {
    setUploadedFiles(files);
    setResult(null);
    setError(null);
  };

  const handleVirtualTryOn = async () => {
    if (!uploadedFiles.person) {
      setError('Per favore carica almeno la tua foto!');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      const response = await axios.post('/api/virtual-tryon', {
        files: uploadedFiles
      });

      if (response.data.success) {
        setResult(response.data.result);
      } else {
        setError(response.data.message || 'Errore durante il processing');
      }
    } catch (err) {
      console.error('Error:', err);
      setError('Errore di connessione al server');
    } finally {
      setIsProcessing(false);
    }
  };

  const resetAll = () => {
    setUploadedFiles({});
    setResult(null);
    setError(null);
  };

  return (
    <div className="virtual-tryon-container">
      <div className="upload-section">
        <h2>Carica le tue immagini</h2>
        <ImageUploader onFilesUploaded={handleFilesUploaded} />
        
        {Object.keys(uploadedFiles).length > 0 && (
          <div className="uploaded-files-preview">
            <h3>Immagini caricate:</h3>
            <div className="files-grid">
              {Object.entries(uploadedFiles).map(([type, file]) => (
                <div key={type} className="file-preview">
                  <img 
                    src={`/uploads/${file.filename}`} 
                    alt={`${type} preview`}
                    className="preview-image"
                  />
                  <p className="file-type">{getFileTypeLabel(type)}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="action-buttons">
          <button 
            onClick={handleVirtualTryOn}
            disabled={!uploadedFiles.person || isProcessing}
            className="try-on-button"
          >
            {isProcessing ? 'Elaborazione in corso...' : 'Prova Virtualmente!'}
          </button>
          
          {Object.keys(uploadedFiles).length > 0 && (
            <button onClick={resetAll} className="reset-button">
              Ricomincia
            </button>
          )}
        </div>

        {error && (
          <div className="error-message">
            <p>{error}</p>
          </div>
        )}
      </div>

      {(result || isProcessing) && (
        <div className="result-section">
          <ResultDisplay result={result} isProcessing={isProcessing} />
        </div>
      )}
    </div>
  );
};

const getFileTypeLabel = (type) => {
  const labels = {
    person: 'La tua foto',
    hat: 'Cappello',
    top: 'Maglietta/Giacca',
    bottom: 'Pantaloni/Gonna',
    shoes: 'Scarpe'
  };
  return labels[type] || type;
};

export default VirtualTryOn;
