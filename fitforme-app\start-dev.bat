@echo off
echo Starting FitForMe Development Environment...
echo.

REM Check if .env exists
if not exist .env (
    echo ERROR: File .env non trovato!
    echo Copia .env.example in .env e inserisci la tua chiave API di Gemini.
    echo.
    pause
    exit /b 1
)

REM Check if node_modules exists
if not exist node_modules (
    echo Installing dependencies...
    npm install
    echo.
)

echo Starting backend server...
start "FitForMe Backend" cmd /k "npm run server"

timeout /t 3 /nobreak > nul

echo Starting frontend development server...
start "FitForMe Frontend" cmd /k "npm run dev"

echo.
echo ✅ Development environment started!
echo.
echo Backend:  http://localhost:3001
echo Frontend: http://localhost:5173
echo.
echo Press any key to close this window...
pause > nul
