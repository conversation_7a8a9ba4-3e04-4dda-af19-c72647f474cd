#!/bin/bash

echo "Starting FitForMe Development Environment..."
echo

# Check if .env exists
if [ ! -f .env ]; then
    echo "ERROR: File .env non trovato!"
    echo "Copia .env.example in .env e inserisci la tua chiave API di Gemini."
    echo
    exit 1
fi

# Check if node_modules exists
if [ ! -d node_modules ]; then
    echo "Installing dependencies..."
    npm install
    echo
fi

echo "Starting backend server..."
npm run server &
BACKEND_PID=$!

sleep 3

echo "Starting frontend development server..."
npm run dev &
FRONTEND_PID=$!

echo
echo "✅ Development environment started!"
echo
echo "Backend:  http://localhost:3001"
echo "Frontend: http://localhost:5173"
echo
echo "Press Ctrl+C to stop both servers..."

# Wait for Ctrl+C
trap 'kill $BACKEND_PID $FRONTEND_PID; exit' INT
wait
